import axios from 'axios';
import store from '@/store';
import { VERSION } from '@/config';
import { storifyDate } from '@/utils/filters';

class ApiClient {
  apiUrl = import.meta.env.VITE_TXI_API;
  timeout = 60_000;
  debug = store?.getters.__state?.appMode === 'DEBUG';
  pendingRequests = new Map();

  /**
   * Make a request to the TOPS API
   */
  async request(noun, verb, data = {}, options = {}) {
    const params = this._buildParameters(noun, verb, data, options);
    const requestKey = this._createRequestKey(noun, verb, data, options);

    // Optionally cancel previous request
    if (options.cancelPrevious) {
      const existing = this.pendingRequests.get(requestKey);
      if (existing) {
        existing.controller.abort();
        if (this.debug) {
          console.log(`Cancelled previous request for ${noun}.${verb}`);
        }
        this.pendingRequests.delete(requestKey);
      }
    }

    // Return existing pending request if present
    if (this.pendingRequests.has(requestKey)) {
      if (this.debug) {
        console.log(`Duplicate request for ${noun}.${verb}, returning existing promise`);
      }
      return this.pendingRequests.get(requestKey).promise;
    }

    // Create new controller and signal
    const controller = new AbortController();
    const signal = options.signal || controller.signal;

    // Initiate request and clean up when done
    const requestPromise = this._makeHttpRequest(params, noun, verb, signal)
      .finally(() => {
        this.pendingRequests.delete(requestKey);
      });

    this.pendingRequests.set(requestKey, { promise: requestPromise, controller });
    return requestPromise;
  }

  /**
   * Make the actual HTTP request
   */
  async _makeHttpRequest(params, noun, verb, signal) {
    try {
      const response = await axios.post(this.apiUrl, params, {
        headers: {
          'X-txi-api': `App:TOPS Browser,Version:${VERSION},Noun:${noun},Verb:${verb}`
        },
        timeout: this.timeout,
        signal
      });

      return response;
    } catch (error) {
      // Handle cancellation gracefully
      if (axios.isCancel(error) || error.code === 'ERR_CANCELED') {
        const cancelError = new Error(`Request cancelled: ${noun}.${verb}`);
        cancelError.name = 'RequestCancelled';
        cancelError.originalError = error;
        throw cancelError;
      }

      // Log error in debug mode
      if (this.debug) {
        console.log(`%cerror → %c${noun}, ${verb})`,
          'font-variant: small-caps; color: #FF4136',
          'font-weight: bold',
          'color: #AAAAAA',
          error);
      }

      throw error;
    }
  }

  /**
   * Create a unique key for request deduplication
   */
  _createRequestKey(noun, verb, data = {}, options = {}) {
    const keyData = {
      noun,
      verb,
      data,
      lastRead: options.lastRead
    };

    return JSON.stringify(keyData);
  }

  /**
   * Build request parameters
   */
  _buildParameters(noun, verb, data = {}, options = {}) {
    const { product, orgUnitKey, appMode: mode, user, instance } = store.state;

    return {
      Operation: {
        Noun: noun,
        Verb: verb,
        ProductKey: product.key,
        OrgUnitKey: orgUnitKey,
        Mode: mode,
        ResponseData: 'JSON',
        LastRead: storifyDate(options.lastRead ?? '')
      },
      Authentication: {
        UserKey: user.Key,
        InstanceKey: instance.Key,
        AuthenticationKey: instance.Authentication
      },
      Data: {
        Parameters: {
          IncludeHeader: false,
          NameValuePairs: true
        },
        ...data
      }
    };
  }
}

// Create a singleton API client
const apiClient = new ApiClient();

/**
 * Create a noun-specific API namespace
 */
function createNounApi(noun) {
  return new Proxy({}, {
    get(_, verb) {
      // Return a function that makes the API request
      return async (data = {}, options = {}) => {
        return apiClient.request(noun, verb, data, options);
      };
    }
  });
}

/**
 * Main export
 */
const tops = new Proxy({}, {
  get(target, noun) {
    // Create and cache the noun API if it doesn't exist
    if (!target[noun]) {
      target[noun] = createNounApi(noun);
    }
    return target[noun];
  }
});

export default tops;
